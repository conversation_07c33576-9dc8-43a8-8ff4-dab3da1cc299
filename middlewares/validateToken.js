const jwt = require('jsonwebtoken')

const validateToken = (req, res, next) => {
  const authorizationHeader = req.headers.authorization
  let result
  if (authorizationHeader) {
    const token = req.headers.authorization.split(' ')[1] // Bearer <token>
    const options = {
      expiresIn: process.env.JWT_EXPIRES,
      issuer: process.env.JWT_ISSUER
    }
    const secret = process.env.JWT_SECRET
    try {
      // verify makes sure that the token hasn't expired and has been issued by us
      result = jwt.verify(token, secret, options)

      // Let's pass back the decoded token to the request object
      req.decoded = result
      // We call next to pass execution to the subsequent middleware
      next()
    } catch (err) {
      // Throw an error just in case anything goes wrong with verification
      throw new Error(err)
    }
  } else {
    result = {
      error: `Authentication error. Token required.`,
      status: 401
    }
    res.status(401).send(result)
  }
}

module.exports = validateToken
