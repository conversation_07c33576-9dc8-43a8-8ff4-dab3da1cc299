{"name": "root-template-express", "version": "1.0.0", "description": "Backend template for Root Kings Express Applications", "main": "server.js", "scripts": {"start": "node server.js", "dev": "nodemon server.js", "lint": "eslint . ; exit 0"}, "author": "K<PERSON><PERSON>h <<EMAIL>>", "license": "MIT", "dependencies": {"cors": "^2.8.5", "dotenv": "^16.5.0", "express": "^5.1.0", "jsonwebtoken": "^9.0.2", "moment": "^2.30.1", "moment-timezone": "^0.5.48", "mongoose": "^8.15.0", "mongoose-lean-virtuals": "^2.0.0", "mongoose-paginate": "^5.0.3", "multer": "^2.0.0", "unique-string": "^3.0.0"}, "devDependencies": {"eslint": "^9.27.0", "morgan": "^1.10.0", "nodemon": "^3.1.10"}}